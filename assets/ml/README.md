# Offline AI Model Integration

This directory is prepared for the Gemini-3n 4b TFLite model integration for offline Tamazight translation.

## Model Files Expected

When the TFLite conversion is complete, place the following files in this directory:

### 1. Model File
- `gemma-3n-4b-tamazight-ft.tflite` - The converted TensorFlow Lite model
- Size: Expected ~2-4GB (quantized version)
- Format: TensorFlow Lite (.tflite)

### 2. Tokenizer Assets
- `tokenizer.json` - Tokenizer configuration
- `vocab.txt` - Vocabulary file (if separate)
- Any additional tokenizer assets from the Hugging Face model

## Model Specifications

### Base Model
- **Model**: Gemini-3n 4b parameter version
- **Release**: June 2025 (Kaggle Google Deep Mind Hackathon)
- **Fine-tuning**: Over 100,000 Arabic, English, French, and Tifinagh language pairs
- **Target Languages**: 
  - Tamazight (ⵜⴰⵎⴰⵣⵉⵖⵜ) - Tachelhit variant
  - Arabic (العربية)
  - French (Français)
  - English

### Performance Targets
- **Inference Time**: < 2 seconds on modern Android devices
- **Memory Usage**: < 4GB RAM
- **Accuracy**: > 85% for common phrases
- **Emergency Phrases**: > 95% accuracy for critical communications

## Integration Status

### ✅ Completed
- Metro configuration for .tflite assets
- Offline AI service architecture
- Mock translation system for development
- Emergency phrase preloading
- Database integration for translation history

### 🔄 In Progress
- TFLite model conversion (expected completion: tomorrow)
- Android native module for model inference
- iOS integration planning

### 📋 Pending TFLite Model
- Replace mock translations with actual model inference
- Implement tokenization and preprocessing
- Add model performance monitoring
- Optimize memory usage for mobile devices

## Usage Instructions

### For Developers
1. Once the TFLite model is ready, place it in this directory
2. Update the `offlineAIService.ts` to load the actual model
3. Test on Android devices first, then iOS
4. Monitor performance and memory usage

### For Testing
The app currently uses mock translations that simulate the expected behavior:
- Common phrases are translated accurately
- Emergency contexts are prioritized
- Fallback handling for unknown text
- Performance simulation (1-2 second delay)

## Emergency Context Priority

The offline model is specifically optimized for emergency situations:

### High Priority Phrases (Earthquake Relief)
- "I need help" → "ⵔⵉⵖ ⵜⵉⵡⵉⵙⵉ"
- "Where is the hospital?" → "ⵎⴰⵏⵉ ⵉⵍⵍⴰ ⵓⵙⴳⵏⴼ"
- "Call the police" → "ⵙⵙⵉⵡⵍ ⵍⴱⵓⵍⵉⵙ"
- "I am lost" → "ⵔⵉⵖ ⵓⵔⵉⵖ"
- "Water" → "ⴰⵎⴰⵏ"
- "Food" → "ⵓⵛⵛⵉ"

### Government/Official Context
- Formal terminology for legal and administrative use
- Constitutional rights and language recognition
- Parliamentary and official procedures

## Technical Implementation

### Current Architecture
```typescript
// Offline AI Service
class OfflineAIService {
  async translateText(text, from, to, context) {
    // TODO: Replace with TFLite model inference
    // Currently using mock translations
  }
}
```

### Future TFLite Integration
```typescript
// Will be implemented once model is ready
import { TFLiteModule } from './native/TFLiteModule';

class OfflineAIService {
  async translateText(text, from, to, context) {
    const tokens = await this.tokenize(text);
    const result = await TFLiteModule.runInference(tokens);
    return this.detokenize(result);
  }
}
```

## Performance Monitoring

The service includes built-in performance monitoring:
- Translation time tracking
- Confidence scoring
- Memory usage monitoring
- Error handling and fallbacks

## Support

For questions about the TFLite integration:
1. Check the conversion progress in the documentation
2. Review the native module implementation guide
3. Test with mock translations first
4. Monitor device performance during testing

---

**Note**: This infrastructure is ready for the TFLite model. The conversion from Gemini-3n 4b to TFLite format is expected to complete tomorrow, after which the actual model files will be placed here and the mock system will be replaced with real AI inference.
