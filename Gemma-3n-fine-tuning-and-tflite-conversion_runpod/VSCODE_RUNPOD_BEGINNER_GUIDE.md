# 🚀 Complete Beginner's Guide: VSCode + Runpod for AI Model Fine-tuning

**For Kaggle Google Deepmind Competition Hackathon**  
**Deadline: August 06, 2025**

This guide will help complete beginners set up VSCode with Runpod to fine-tune Tamazight language models and convert them to TFLite for mobile apps.

## 🎯 What You'll Accomplish

By the end of this guide, you'll have:
- ✅ Fine-tuned Gemma 3N models for Tamazight language
- ✅ Models automatically uploaded to Hugging Face
- ✅ TFLite models ready for mobile deployment
- ✅ A complete understanding of the AI fine-tuning workflow

## 📋 Prerequisites (What You Need)

### 1. Accounts (All Free to Start)
- **Runpod Account**: [runpod.io](https://runpod.io) - For GPU computing
- **Hugging Face Account**: [huggingface.co](https://huggingface.co) - For model storage
- **VSCode**: [code.visualstudio.com](https://code.visualstudio.com) - Code editor

### 2. Basic Knowledge
- Basic understanding of Python (beginner level is fine)
- Familiarity with Jupyter notebooks
- Basic command line usage

## 🔧 Step 1: Set Up Your Runpod Environment

### 1.1 Create Runpod Account
1. Go to [runpod.io](https://runpod.io)
2. Sign up for a free account
3. Add payment method (you'll get credits to start)

### 1.2 Get Your Hugging Face Token
1. Go to [huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
2. Click "New token"
3. Name it "runpod-finetuning"
4. Select "**Write**" permissions (needed for uploading models)
5. Copy the token (starts with `hf_`)
6. **IMPORTANT**: Save this token securely - you'll need it for Runpod

### 1.3 🔐 CRITICAL: Set Up Secrets (SECURITY FIRST!)
**⚠️ MUST DO BEFORE CREATING POD - Environment variables are NOT encrypted!**

1. In Runpod dashboard, click "**Secrets**" in the left sidebar
2. Click "**Add Secret**"
3. **Secret Name**: `HF_TOKEN`
4. **Secret Value**: Paste your Hugging Face token (starts with `hf_`)
5. Click "**Save Secret**"

**✅ Why Secrets?** Your HF token will be encrypted and secure (not visible in pod settings)

### 1.4 🔑 Set Up SSH Access (REQUIRED FOR VSCODE)
**⚠️ MUST DO BEFORE CREATING POD - SSH access will be grayed out otherwise!**

1. In Runpod dashboard, click "**Settings**" in the left sidebar
2. Scroll down to "**SSH Public Keys**" section
3. **If you have SSH keys already:**
   - Paste your public key in the text box
   - Click "**Update Public Key**"
4. **If you need to create SSH keys:**
   - Follow the SSH key generation guide below
   - Then return here to add the public key

#### 🔧 Quick SSH Key Generation (if needed):
**On Mac/Linux:**
```bash
# Step 1: Generate the SSH key
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# When prompted:
# "Enter file in which to save the key": Just press ENTER (use default)
# "Enter passphrase": Just press ENTER (no passphrase for simplicity)
# "Enter same passphrase again": Just press ENTER

# Step 2: Copy your public key
cat ~/.ssh/id_rsa.pub
```

**On Windows:**
```powershell
# Step 1: Generate the SSH key
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# When prompted:
# "Enter file in which to save the key": Just press ENTER (use default)
# "Enter passphrase": Just press ENTER (no passphrase for simplicity)
# "Enter same passphrase again": Just press ENTER

# Step 2: Copy your public key
type %USERPROFILE%\.ssh\id_rsa.pub
```

**⚠️ IMPORTANT**:
- The `cat ~/.ssh/id_rsa.pub` command is run AFTER key generation
- Don't type it when prompted for the file location - just press ENTER!

**🤔 Common Questions:**

**Q: The email in my SSH key is different from what I entered - does it matter?**
**A: No, it doesn't matter at all!** The email is just a label/comment. If you see an existing SSH key with a different email (maybe from GitHub setup), that's perfectly fine! Just copy and use it.

**Q: What if I already have SSH keys?**
**A: Great!** Just run `cat ~/.ssh/id_rsa.pub` to see your existing public key and use that. No need to create new ones.

**Q: I see "ssh-rsa AAAAB3..." followed by an email - what do I copy?**
**A: Copy the ENTIRE line** - including the email at the end. It should look like:
```
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDxxxxx...xxxxx <EMAIL>
```

### 1.5 Deploy Your Runpod Instance
1. In Runpod dashboard, click "**Pods**" in the left sidebar
2. Click "**Deploy a Pod**" (now visible in the main area)
3. Choose "**GPU**" (should be selected by default)
4. **Template Selection** (IMPORTANT - Choose the right one):
   - **🎯 RECOMMENDED**: "**Runpod VS Code Server**" (Perfect for VSCode workflow)
   - **Alternative**: "**Runpod PyTorch 2.8.0**" (Latest PyTorch, also works well)
   - **Avoid**: Older PyTorch versions (2.1, 2.2.0, 2.4.0) - may have compatibility issues
5. **GPU Selection** (IMPORTANT - Different needs for each phase):

   **🔥 For Fine-tuning Phase:**
   - **🎯 PERFECT**: RTX 4090 (24GB VRAM) - $0.69/hr
   - **Alternative**: RTX 6000 Ada (48GB VRAM) - $0.77/hr
   - **Budget**: RTX 4000 Ada (20GB VRAM) - $0.26/hr (may be tight for 4B model)

   **🚀 For TFLite Conversion Phase:**
   - **🎯 BEST VALUE**: H100 PCIe (80GB VRAM, 188GB RAM) - $2.39/hr
   - **PREMIUM**: H100 NVL (94GB VRAM, 94GB RAM) - $2.79/hr
   - **ALTERNATIVE**: H100 SXM (80GB VRAM, 125GB RAM) - $2.69/hr

   **💡 Pro Tip**: Use RTX 4090 for fine-tuning, then switch to H100 for TFLite conversion!
6. **Container Disk**: 50GB minimum
7. **Volume Disk**: 100GB minimum

### 1.6 🔧 Configure Pod Template (SECURE SETUP!)
**Now you can safely configure environment variables using your encrypted secret:**

1. After selecting your GPU, click "**Edit Template**"
2. In the "**Environment Variables**" section:
   - **IGNORE the warning** about "not encrypted" - we're using secrets!
   - Click "**Add Environment Variable**"
   - **Name**: `HF_TOKEN`
   - **Value**: Click the dropdown and select "**Use a Secret**"
   - Select your `HF_TOKEN` secret from the dropdown
3. **✅ Verify SSH Access**: The SSH terminal access should now be enabled (not grayed out)
4. Click "**Save Overrides**"

### 1.7 Final Pod Configuration
1. **Review your settings**:
   - ✅ Template: Runpod VS Code Server
   - ✅ GPU: RTX 4090 (or your choice)
   - ✅ Environment Variable: HF_TOKEN (using secret)
   - ✅ SSH Access: Enabled
2. **Click "Deploy"**
3. **Wait for pod to start** (usually 1-2 minutes)
4. **✅ Success indicators**:
   - Pod status shows "Running"
   - SSH terminal access is available
   - Environment variables are securely configured

## 💻 Step 2: Connect VSCode to Runpod

### 2.1 Install VSCode Extensions
1. Open VSCode
2. Install these extensions:
   - "Remote - SSH"
   - "Python"
   - "Jupyter"

### 2.2 Connect to Your Runpod
1. In Runpod dashboard, find your running pod
2. Click "Connect" → "Connect via SSH"
3. Copy the SSH command (looks like: `ssh <EMAIL> -p 12345`)
4. In VSCode:
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Remote-SSH: Connect to Host"
   - Paste the SSH command
   - Enter password when prompted

### 2.3 Set Up Your Workspace
1. Once connected, open terminal in VSCode
2. Clone the repository:
   ```bash
   git clone https://github.com/your-repo/tamazight-multilingo-app.git
   cd tamazight-multilingo-app/Gemma-3n-fine-tuning-and-tflite-conversion_runpod
   ```

## 🎯 Step 3: Run the Fine-tuning Process

### 3.1 Choose Your Model Size
**For Beginners**: Start with 2B model (faster, less memory)
**For Advanced**: Use 4B model (better quality, more memory needed)

### 3.2 Open the Notebook
1. In VSCode file explorer, navigate to:
   - `v6_Gemma_3N_(2B)_Conversational_072725_gguf_wkg.ipynb` (2B model)
   - OR `v5_Gemma_3N_(4B)_Conversational_072725_gguf_wkg.ipynb` (4B model)

### 3.3 Run the Notebook
1. Click on the notebook file
2. VSCode will ask to select a kernel - choose Python
3. **Important**: Run cells one by one, don't run all at once
4. Read the output of each cell before proceeding

### 3.4 What Each Section Does
1. **Installation**: Installs required libraries
2. **Authentication**: Connects to Hugging Face
3. **Model Loading**: Downloads the base Gemma model
4. **Data Loading**: Loads Tamazight datasets from HF Hub
5. **Training**: Fine-tunes the model (takes 30-60 minutes)
6. **Saving**: Uploads trained models to your HF repositories

## 📱 Step 4: Convert to TFLite (Mobile Ready)

### 4.1 After Fine-tuning is Complete
1. Open the TFLite conversion notebook:
   - `v6-gemma-3n-2b-tflite-conversion-runpod-to-check.ipynb` (for 2B)
   - OR `v5-gemma-3n-4b-tflite-conversion-runpod-to-check.ipynb` (for 4B)

### 4.2 Run TFLite Conversion
1. This notebook will:
   - Load your fine-tuned model from Hugging Face
   - Convert it to TFLite format
   - Save files ready for mobile apps

### 4.3 Download Your Results
1. After conversion, you'll have:
   - `.tflite` file (the mobile model)
   - `tokenizer_assets` folder (for text processing)
2. Download these files from VSCode file explorer

## 🔍 Step 5: Verify Everything Works

### 5.1 Check Your Hugging Face Repositories
Visit your HF profile and verify these repositories were created/updated:
- `tamazightdev/v6-gemma-3n-2b-tmz-ft-pt-lora` (LoRA model)
- `tamazightdev/v6-gemma-3n-2b-tmz-ft-vllm-merged` (Merged model)
- `tamazightdev/v6-gemma-3n-2b-tmz-ft-gguf` (GGUF model)

### 5.2 Test Your TFLite Model
The TFLite model can be integrated into:
- Android apps
- iOS apps
- Web applications
- Edge devices

## 🛠️ Troubleshooting for Beginners

### Common Issues and Solutions

**1. "HF_TOKEN not found"**
- **Problem**: Environment variable not set
- **Solution**: Go back to Runpod pod settings, add HF_TOKEN, restart pod

**2. "CUDA out of memory"**
- **Problem**: GPU doesn't have enough memory
- **Solution**: Use 2B model instead of 4B, or upgrade to larger GPU

**3. "SSH connection failed"**
- **Problem**: Network or authentication issue
- **Solution**: Check SSH command, verify pod is running, try reconnecting

**4. "Notebook kernel not found"**
- **Problem**: Python environment not set up
- **Solution**: In VSCode, select Python interpreter, install Jupyter extension

**5. "Dataset loading failed"**
- **Problem**: Authentication or network issue
- **Solution**: Verify HF_TOKEN is correct, check internet connection

### Getting Help
1. **Discord Communities**: Join AI/ML Discord servers
2. **Runpod Support**: Use Runpod's help documentation
3. **Hugging Face Forums**: Ask questions on HF community forums
4. **GitHub Issues**: Check the repository for known issues

## 💰 Cost Estimation

### Typical Costs for Beginners
- **2B Model Fine-tuning**: $2-5 per run (30-60 minutes)
- **4B Model Fine-tuning**: $5-10 per run (60-90 minutes)
- **TFLite Conversion**: $0.50-1 per conversion (5-15 minutes)

### Money-Saving Tips
1. **Start with 2B model** to learn the process
2. **Stop pods when not in use** to avoid idle charges
3. **Use spot instances** for cheaper rates
4. **Monitor usage** in Runpod dashboard

## 🎯 Success Checklist

After completing this guide, you should have:
- [ ] Successfully connected VSCode to Runpod
- [ ] Fine-tuned a Tamazight language model
- [ ] Uploaded models to Hugging Face repositories
- [ ] Converted model to TFLite format
- [ ] Downloaded mobile-ready model files
- [ ] Understanding of the complete AI fine-tuning workflow

## 🏆 Competition Tips

1. **Start Early**: Begin with 2B model to learn the process
2. **Document Everything**: Keep notes of what works
3. **Test Thoroughly**: Verify models work before submission
4. **Backup Models**: Save copies of successful models
5. **Join Community**: Connect with other participants on Discord

## 📚 Next Steps

After mastering this workflow:
1. **Experiment with different parameters** (learning rate, epochs)
2. **Try different model sizes** (2B vs 4B)
3. **Explore other languages** or use cases
4. **Integrate models into mobile apps**
5. **Share your results** with the community

**Good luck with the competition!** 🚀

---

*This guide was created to help beginners participate in the Kaggle Google Deepmind Competition Hackathon. For updates and community support, join the Discord channels mentioned in the competition.*
