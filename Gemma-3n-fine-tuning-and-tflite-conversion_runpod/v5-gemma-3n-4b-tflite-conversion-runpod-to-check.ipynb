# ⚠️ MANDATORY SAFETY CHECK ⚠️
print("🚨 WARNING: This notebook has known compatibility issues!")
print("🚨 Gemma-3N TFLite conversion is currently NOT supported!")
print("🚨 Multiple GitHub issues confirm this doesn't work!")
print("")
print("Recommended alternatives:")
print("1. Use GGUF format instead of TFLite")
print("2. Wait for official Gemma-3N support")
print("3. Use Gemma-3 (not 3N) if TFLite is required")
print("")
response = input("Type 'I UNDERSTAND THE RISKS' to continue anyway: ")
if response != "I UNDERSTAND THE RISKS":
    raise SystemExit("Notebook execution stopped for safety.")
print("⚠️  Proceeding with experimental/likely-to-fail conversion...")

# Step 1: Install all required modern libraries for Runpod GPU environment
# We install transformers directly from the GitHub main branch for the newest models.

print("🚀 Installing libraries optimized for Runpod GPU environment...")

!pip install --upgrade pip
!pip install git+https://github.com/huggingface/transformers.git

# Install the correct 'ai-edge-torch' package and other essentials.
!pip install --upgrade torch accelerate bitsandbytes sentencepiece "ai-edge-torch>=0.2.1" timm huggingface_hub

# Verify GPU availability for Runpod
import torch
print(f"\n🔍 GPU Check:")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU device: {torch.cuda.get_device_name(0)}")
    print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    print("⚠️  No GPU detected. This notebook requires a GPU for optimal performance.")

print("\n✅ Libraries installed successfully.")

# Step 1a: Fix Dependency Conflict
# The installation of tf-nightly can pull a version of protobuf that conflicts 
# with other libraries. We force-reinstall a compatible version to prevent the
# 'GetPrototype' error that would otherwise stop the conversion process.
!pip install --upgrade protobuf==4.21.5

print("✅ Protobuf version conflict resolved.")

# Step 2: Authenticate with Hugging Face (Runpod Version)
# This cell retrieves the Hugging Face token from a Runpod environment variable.
# 
# SETUP INSTRUCTIONS FOR RUNPOD:
# 1. In your Runpod pod settings, add an environment variable:
#    - Name: HF_TOKEN
#    - Value: Your Hugging Face token (get it from https://huggingface.co/settings/tokens)
# 2. Make sure to restart your pod after adding the environment variable
# 3. Alternatively, you can set it manually by uncommenting the line below:
#    os.environ['HF_TOKEN'] = 'your_token_here'

import os
from huggingface_hub import login

try:
    # Retrieve the token from the environment variable set in the pod settings
    hf_token = os.getenv("HF_TOKEN")
    
    # Alternative: Uncomment the line below and add your token manually if environment variable is not set
    # hf_token = "your_hugging_face_token_here"

    if hf_token and hf_token != "your_hugging_face_token_here":
        login(token=hf_token)
        print("✅ Successfully authenticated with Hugging Face.")
        print(f"✅ Token starts with: {hf_token[:10]}...")
    else:
        print("❌ Hugging Face token not found or not properly set.")
        print("\n📋 SETUP INSTRUCTIONS:")
        print("1. Get your HF token from: https://huggingface.co/settings/tokens")
        print("2. In Runpod pod settings, add environment variable: HF_TOKEN")
        print("3. Restart your pod after adding the environment variable")
        print("4. Or manually set: os.environ['HF_TOKEN'] = 'your_token_here'")
        # You may want to stop execution if the token is missing
        raise ValueError("HF_TOKEN environment variable not set properly.")

except Exception as e:
    print(f"❌ An error occurred during authentication: {e}")
    print("\n🔧 Troubleshooting:")
    print("- Verify your HF token is valid and has the necessary permissions")
    print("- Check that the environment variable is properly set in Runpod")
    print("- Ensure you have access to the model repository")

# Step 3: Load Model, Convert, and Save (with Wrapper)
import torch
import os
# ⚠️ WARNING: Gemma3nForConditionalGeneration doesn't exist! Using AutoModelForCausalLM
from transformers import AutoTokenizer, AutoModelForCausalLM
import ai_edge_torch

# --- Configuration ---
MODEL_ID = "tamazightdev/v5-gemma-3n-4b-tmz-ft-vllm-merged"
OUTPUT_TFLITE_MODEL = "gemma-3n-4b-tamazight-ft.tflite"
TOKENIZER_ASSETS_DIR = "tokenizer_assets"

print(f"--- Starting conversion for model: {MODEL_ID} ---")

# --- Define the Traceable Wrapper ---
class Gemma3nForTFLite(torch.nn.Module):
    """A traceable wrapper for Gemma 3n for single-step autoregressive decoding."""
    def __init__(self, model_path: str):
        super().__init__()
        print(f"Loading model from {model_path}...")
        # ⚠️ Using AutoModelForCausalLM instead of non-existent Gemma3nForConditionalGeneration
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32 # Load in FP32 for stable conversion
        ).eval()
        print("✅ Model loaded successfully into wrapper.")

    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor):
        """Performs a single forward pass to get the next token logits."""
        outputs = self.model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            use_cache=False
        )
        # Return logits for the last token in the sequence [batch_size, vocab_size]
        return outputs.logits[:, -1, :]

try:
    # 1. Load the tokenizer and the wrapped model
    print("\n1. Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    traceable_model = Gemma3nForTFLite(MODEL_ID)
    print("✅ Tokenizer and wrapped model loaded.")

    # 2. Prepare an example input for the converter to trace the model's graph.
    print("\n2. Preparing example input for tracing...")
    # The wrapper's forward() method expects both input_ids and attention_mask.
    sample_input_ids = torch.randint(0, 32000, (1, 128), dtype=torch.long)
    sample_attention_mask = torch.ones((1, 128), dtype=torch.long)
    sample_inputs = (sample_input_ids, sample_attention_mask)
    print("✅ Example input prepared.")

    # 3. Convert the wrapped model to TFLite format
    print(f"\n3. Converting model to TFLite format...")
    edge_model_bytes = ai_edge_torch.convert(
        traceable_model,
        sample_inputs
    )
    print("✅ Model successfully converted.")

    # 4. Save the TFLite model to a file
    print(f"\n4. Saving TFLite model to {OUTPUT_TFLITE_MODEL}...")
    with open(OUTPUT_TFLITE_MODEL, "wb") as f:
        f.write(edge_model_bytes)
    print("✅ TFLite model saved.")
    
    # 5. Save the tokenizer assets for your Android application
    print(f"\n5. Saving tokenizer assets to {TOKENIZER_ASSETS_DIR}...")
    if not os.path.exists(TOKENIZER_ASSETS_DIR):
        os.makedirs(TOKENIZER_ASSETS_DIR)
    tokenizer.save_pretrained(TOKENIZER_ASSETS_DIR)
    print(f"✅ Tokenizer assets saved.")
    
    print("\n--- Conversion Complete! ---")

except Exception as e:
    import traceback
    print(f"\n--- An Error Occurred ---")
    print(f"Error during conversion: {e}")
    traceback.print_exc()
    print("\nPlease check the model path, your Hugging Face token permissions, and available RAM.")