{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook for Gemma-3n TFLite Conversion (Runpod Version)\n", "\n", "This notebook has been updated to use the latest libraries for converting new Hugging Face models like Gemma-2 (`gemma-3n`) to the TFLite format and is configured to run on a **Runpod** environment.\n", "\n", "**Key Changes:**\n", "1.  **Upgraded Libraries:** We now install `ai-edge-converter`, which is the modern successor to `ai-edge-torch` and has support for the `Gemma2` architecture.\n", "2.  **Latest `transformers`:** We continue to install `transformers` from the main branch to ensure compatibility with the newest model releases.\n", "3.  **Runpod Authentication:** Step #2 is now adapted to use Runpod's environment variables for Hugging Face authentication, removing the Kaggle-specific code.\n", "4.  **Dependency Fix:** A new cell (Step 1a) has been added to fix a common `protobuf` version conflict that causes the conversion to fail."]}, {"cell_type": "code", "execution_count": null, "id": "0e39a685", "metadata": {}, "outputs": [], "source": ["# Step 1: Install all required modern libraries for Runpod GPU environment\n", "# We install transformers directly from the GitHub main branch for the newest models.\n", "\n", "print(\"🚀 Installing libraries optimized for Runpod GPU environment...\")\n", "\n", "!pip install --upgrade pip\n", "!pip install git+https://github.com/huggingface/transformers.git\n", "\n", "# Install the correct 'ai-edge-torch' package and other essentials.\n", "!pip install --upgrade torch accelerate bitsandbytes sentencepiece \"ai-edge-torch>=0.2.1\" timm huggingface_hub\n", "\n", "# Verify GPU availability for Runpod\n", "import torch\n", "print(f\"\\n🔍 GPU Check:\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"GPU device: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")\n", "else:\n", "    print(\"⚠️  No GPU detected. This notebook requires a GPU for optimal performance.\")\n", "\n", "print(\"\\n✅ Libraries installed successfully.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 1a: Fix Dependency Conflict\n", "# The installation of tf-nightly can pull a version of protobuf that conflicts \n", "# with other libraries. We force-reinstall a compatible version to prevent the\n", "# 'GetPrototype' error that would otherwise stop the conversion process.\n", "!pip install --upgrade protobuf==4.21.5\n", "\n", "print(\"✅ Protobuf version conflict resolved.\")"]}, {"cell_type": "code", "execution_count": null, "id": "845ff95d", "metadata": {}, "outputs": [], "source": ["# Step 2: Authenti<PERSON> with <PERSON><PERSON> Face (Runpod Version)\n", "# This cell retrieves the Hugging Face token from a Runpod environment variable.\n", "# \n", "# SETUP INSTRUCTIONS FOR RUNPOD:\n", "# 1. In your Runpod pod settings, add an environment variable:\n", "#    - Name: HF_TOKEN\n", "#    - Value: Your Hugging Face token (get it from https://huggingface.co/settings/tokens)\n", "# 2. Make sure to restart your pod after adding the environment variable\n", "# 3. Alternatively, you can set it manually by uncommenting the line below:\n", "#    os.environ['HF_TOKEN'] = 'your_token_here'\n", "\n", "import os\n", "from huggingface_hub import login\n", "\n", "try:\n", "    # Retrieve the token from the environment variable set in the pod settings\n", "    hf_token = os.getenv(\"HF_TOKEN\")\n", "    \n", "    # Alternative: Uncomment the line below and add your token manually if environment variable is not set\n", "    # hf_token = \"your_hugging_face_token_here\"\n", "\n", "    if hf_token and hf_token != \"your_hugging_face_token_here\":\n", "        login(token=hf_token)\n", "        print(\"✅ Successfully authenticated with Hugging Face.\")\n", "        print(f\"✅ Token starts with: {hf_token[:10]}...\")\n", "    else:\n", "        print(\"❌ Hugging Face token not found or not properly set.\")\n", "        print(\"\\n📋 SETUP INSTRUCTIONS:\")\n", "        print(\"1. Get your HF token from: https://huggingface.co/settings/tokens\")\n", "        print(\"2. In Runpod pod settings, add environment variable: HF_TOKEN\")\n", "        print(\"3. Restart your pod after adding the environment variable\")\n", "        print(\"4. Or manually set: os.environ['HF_TOKEN'] = 'your_token_here'\")\n", "        # You may want to stop execution if the token is missing\n", "        raise ValueError(\"HF_TOKEN environment variable not set properly.\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ An error occurred during authentication: {e}\")\n", "    print(\"\\n🔧 Troubleshooting:\")\n", "    print(\"- Verify your HF token is valid and has the necessary permissions\")\n", "    print(\"- Check that the environment variable is properly set in Runpod\")\n", "    print(\"- Ensure you have access to the model repository\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 3: Load Model, Convert, and Save (with <PERSON><PERSON><PERSON>)\n", "import torch\n", "import os\n", "from transformers import AutoTokenizer, Gemma3nForConditionalGeneration\n", "import ai_edge_torch\n", "\n", "# --- Configuration ---\n", "MODEL_ID = \"tamazightdev/v5-gemma-3n-4b-tmz-ft-vllm-merged\"\n", "OUTPUT_TFLITE_MODEL = \"gemma-3n-4b-tamazight-ft.tflite\"\n", "TOKENIZER_ASSETS_DIR = \"tokenizer_assets\"\n", "\n", "print(f\"--- Starting conversion for model: {MODEL_ID} ---\")\n", "\n", "# --- Define the Traceable Wrapper ---\n", "class Gemma3nForTFLite(torch.nn.Module):\n", "    \"\"\"A traceable wrapper for Gemma 3n for single-step autoregressive decoding.\"\"\"\n", "    def __init__(self, model_path: str):\n", "        super().__init__()\n", "        print(f\"Loading model from {model_path}...\")\n", "        self.model = Gemma3nForConditionalGeneration.from_pretrained(\n", "            model_path,\n", "            torch_dtype=torch.float32 # Load in FP32 for stable conversion\n", "        ).eval()\n", "        print(\"✅ Model loaded successfully into wrapper.\")\n", "\n", "    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor):\n", "        \"\"\"Performs a single forward pass to get the next token logits.\"\"\"\n", "        outputs = self.model(\n", "            input_ids=input_ids,\n", "            attention_mask=attention_mask,\n", "            use_cache=False\n", "        )\n", "        # Return logits for the last token in the sequence [batch_size, vocab_size]\n", "        return outputs.logits[:, -1, :]\n", "\n", "try:\n", "    # 1. Load the tokenizer and the wrapped model\n", "    print(\"\\n1. Loading tokenizer...\")\n", "    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)\n", "    traceable_model = Gemma3nForTFLite(MODEL_ID)\n", "    print(\"✅ Tokenizer and wrapped model loaded.\")\n", "\n", "    # 2. Prepare an example input for the converter to trace the model's graph.\n", "    print(\"\\n2. Preparing example input for tracing...\")\n", "    # The wrapper's forward() method expects both input_ids and attention_mask.\n", "    sample_input_ids = torch.randint(0, 32000, (1, 128), dtype=torch.long)\n", "    sample_attention_mask = torch.ones((1, 128), dtype=torch.long)\n", "    sample_inputs = (sample_input_ids, sample_attention_mask)\n", "    print(\"✅ Example input prepared.\")\n", "\n", "    # 3. Convert the wrapped model to TFLite format\n", "    print(f\"\\n3. Converting model to TFLite format...\")\n", "    edge_model_bytes = ai_edge_torch.convert(\n", "        traceable_model,\n", "        sample_inputs\n", "    )\n", "    print(\"✅ Model successfully converted.\")\n", "\n", "    # 4. Save the TFLite model to a file\n", "    print(f\"\\n4. Saving TFLite model to {OUTPUT_TFLITE_MODEL}...\")\n", "    with open(OUTPUT_TFLITE_MODEL, \"wb\") as f:\n", "        f.write(edge_model_bytes)\n", "    print(\"✅ TFLite model saved.\")\n", "    \n", "    # 5. Save the tokenizer assets for your Android application\n", "    print(f\"\\n5. Saving tokenizer assets to {TOKENIZER_ASSETS_DIR}...\")\n", "    if not os.path.exists(TOKENIZER_ASSETS_DIR):\n", "        os.makedirs(TOKENIZER_ASSETS_DIR)\n", "    tokenizer.save_pretrained(TOKENIZER_ASSETS_DIR)\n", "    print(f\"✅ Tokenizer assets saved.\")\n", "    \n", "    print(\"\\n--- Conversion Complete! ---\")\n", "\n", "except Exception as e:\n", "    import traceback\n", "    print(f\"\\n--- An Error Occurred ---\")\n", "    print(f\"Error during conversion: {e}\")\n", "    traceback.print_exc()\n", "    print(\"\\nPlease check the model path, your Hugging Face token permissions, and available RAM.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Next Steps\n", "\n", "After running the cells above, you should have your converted assets ready in the Runpod file system (check the file panel on the left in JupyterLab):\n", "\n", "1.  **`gemma-3n-4b-tamazight-ft.tflite`**: This is your quantized, on-device model.\n", "2.  A folder named **`tokenizer_assets`**: This contains `tokenizer.json` and other necessary files for your app.\n", "\n", "You will need to **download both the `.tflite` file and the `tokenizer_assets` folder** to integrate them into your Android project."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}