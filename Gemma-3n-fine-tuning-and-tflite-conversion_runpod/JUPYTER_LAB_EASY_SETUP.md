# 🎯 EASIEST Way: RunPod + Jupyter Lab Setup Guide

**For Kaggle Google Deepmind Competition Hackathon**  
**Deadline: August 06, 2025**

## 🚀 Why This Method is BETTER

**❌ Skip the Complex SSH Setup!**  
**✅ Use Jupyter Lab HTTP Connection Instead!**

### Problems with SSH Method:
- ❌ Requires SSH key generation and setup
- ❌ Password prompts and authentication issues  
- ❌ VSCode configuration complexity
- ❌ Terminal commands and technical knowledge needed

### ✅ Jupyter Lab HTTP Method Benefits:
- ✅ **No SSH setup required** - works immediately
- ✅ **No passwords or keys** - direct browser access
- ✅ **Perfect for notebooks** - designed for `.ipynb` files
- ✅ **Easy file upload** - drag and drop interface
- ✅ **Beginner-friendly** - visual, point-and-click interface

## 📋 Quick Prerequisites

1. **RunPod Account**: [runpod.io](https://runpod.io) (free signup)
2. **Hugging Face Token**: [huggingface.co/settings/tokens](https://huggingface.co/settings/tokens) (free)
3. **Your .ipynb notebook files** (download from this repository)

## 🔧 Step 1: Set Up RunPod Pod (5 minutes)

### 1.1 Create Your Pod
1. **Log into RunPod** → Click "**Pods**" → "**Deploy a Pod**"
2. **Choose Template**: Select "**RunPod PyTorch 2.8.0**" (latest and most stable)
3. **Select GPU**: 
   - **For 2B model**: RTX 4090 (24GB) - $0.69/hr ⭐ **RECOMMENDED**
   - **For 4B model**: H100 PCIe (80GB) - $2.39/hr
4. **Set Storage**: Container disk 50GB, Volume disk 100GB

### 1.2 Add Your Hugging Face Token
1. **Get your HF token**: Go to [huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
2. **Create new token** with "Read" permissions, copy it (starts with `hf_`)
3. **In RunPod pod settings**: Find "Environment Variables" section
4. **Add variable**: 
   - Name: `HF_TOKEN`
   - Value: Your token (paste the `hf_` token)
5. **Deploy the pod** and wait for "Running" status

## 💻 Step 2: Connect via Jupyter Lab (30 seconds!)

### 2.1 🎯 The EASY Connection Method
1. **Find your running pod** in RunPod dashboard
2. **Click "Connect"** button on your pod
3. **Click "HTTP Services"** tab (NOT SSH!)
4. **Click "Jupyter Lab → 8888"** 
5. **Jupyter Lab opens in your browser** - You're connected! 🎉

### 2.2 What You'll See
- File browser on the left showing `/workspace` directory
- Launcher in the center with options for Notebook, Console, etc.
- Ready to upload and run your notebooks immediately

## 📁 Step 3: Upload Your Notebook (1 minute)

### 3.1 Upload Your .ipynb File
**Method 1: Upload Button (Easiest)**
1. **Click the Upload button** (📁 icon) in Jupyter Lab toolbar
2. **Select your notebook file** (e.g., `v6_Gemma_3N_(2B)_Conversational_072725_gguf_wkg.ipynb`)
3. **File uploads automatically** to `/workspace`
4. **Double-click the file** to open it

**Method 2: Drag and Drop**
1. **Drag your .ipynb file** from your computer
2. **Drop it in the file browser** (left panel)
3. **Double-click to open**

## 🚀 Step 4: Run Your Notebook

### 4.1 🎯 Recommended Starting Notebooks
**For Beginners (Start Here):**
- `v6_Gemma_3N_(2B)_Conversational_072725_gguf_wkg.ipynb` - Fine-tune 2B model
- `v6-gemma-3n-2b-tflite-conversion-runpod-to-check.ipynb` - Convert to mobile

**For Advanced Users:**
- `v5_Gemma_3N_(4B)_Conversational_072725_gguf_wkg.ipynb` - Fine-tune 4B model  
- `v5-gemma-3n-4b-tflite-conversion-runpod-to-check.ipynb` - Convert to mobile

### 4.2 How to Run
1. **Open your notebook** by double-clicking
2. **Select Python kernel** when prompted
3. **Run cells one by one** - click each cell and press Shift+Enter
4. **Read the output** before proceeding to next cell
5. **Wait for completion** - fine-tuning takes 30-60 minutes

## ✅ Step 5: Verify Success

### 5.1 Check Your Results
After running the notebooks, you should see:
- **Fine-tuned models** uploaded to your Hugging Face repositories
- **TFLite files** ready for download in `/workspace`
- **Success messages** in notebook outputs

### 5.2 Download Your Mobile Models
1. **Find .tflite files** in Jupyter Lab file browser
2. **Right-click the files** → "Download"
3. **Use in your mobile app** - ready for deployment!

## 🛠️ Troubleshooting

### Common Issues & Quick Fixes

**"HF_TOKEN not found"**
- ✅ **Fix**: Add HF_TOKEN environment variable in pod settings, restart pod

**"CUDA out of memory"**  
- ✅ **Fix**: Use 2B model instead of 4B, or upgrade to larger GPU

**"Can't upload files"**
- ✅ **Fix**: Make sure you're in `/workspace` directory in file browser

**"Notebook won't open"**
- ✅ **Fix**: Select Python 3 kernel when prompted

**"Connection lost"**
- ✅ **Fix**: Refresh browser tab, Jupyter Lab will reconnect automatically

## 💰 Cost Estimate

**For 2B Model (Recommended):**
- Fine-tuning: RTX 4090 × 1 hour = ~$0.70
- TFLite conversion: Same GPU × 0.5 hour = ~$0.35
- **Total: ~$1.05** 💰

**For 4B Model (Advanced):**
- Fine-tuning: H100 × 1.5 hours = ~$3.60
- TFLite conversion: Same GPU × 0.5 hour = ~$1.20
- **Total: ~$4.80** 💰

## 🎯 Success Checklist

After completing this guide:
- [ ] ✅ Connected to RunPod via Jupyter Lab (no SSH needed!)
- [ ] ✅ Uploaded and ran fine-tuning notebook
- [ ] ✅ Models uploaded to Hugging Face automatically  
- [ ] ✅ Ran TFLite conversion notebook
- [ ] ✅ Downloaded .tflite files for mobile app
- [ ] ✅ Ready for competition submission!

## 🏆 Competition Tips

1. **Start with 2B model** - faster and cheaper to learn with
2. **Test the process end-to-end** before the deadline
3. **Keep your HF token secure** - don't share it
4. **Save your .tflite files** - backup your final models
5. **Document what works** - for future reference

---

**🎉 Congratulations!** You've successfully set up the easiest RunPod workflow for the competition. No SSH complexity, no password issues - just pure focus on your AI models!
