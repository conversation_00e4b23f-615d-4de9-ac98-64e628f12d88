# 🚀 Runpod Setup Guide for Kaggle Google Deepmind Competition Hackathon

**Competition Deadline: August 06, 2025**  
**Date: July 31, 2025**

This guide will help you set up your Runpod GPU environment to run the Tamazight MultiLingo App notebooks for the competition.

## 📋 Prerequisites

1. **Runpod Account**: Sign up at [runpod.io](https://runpod.io)
2. **Hugging Face Account**: Get your token from [huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
3. **Dataset Files**: Prepare your Tamazight dataset files

## 🔧 Step 1: Runpod Pod Configuration

### 1.1 Create a New Pod
1. Log into your Runpod account
2. Click "**Pods**" in the left sidebar
3. Click "**Deploy a Pod**" (now visible in the main area)
4. Choose "**GPU**" (should be selected by default)
5. **Template Selection** (Choose carefully):
   - **🎯 BEST FOR VSCODE**: "**Runpod VS Code Server**" - Pre-configured for VSCode workflow
   - **ALTERNATIVE**: "**Runpod PyTorch 2.8.0**" - Latest PyTorch with good compatibility
   - **AVOID**: Older versions (PyTorch 2.1, 2.2.0, 2.4.0) - may cause dependency conflicts
6. **GPU Selection Strategy** (Different GPUs for different phases):

   **Phase 1 - Fine-tuning (1-2 hours):**
   - **🎯 RECOMMENDED**: RTX 4090 (24GB VRAM) - $0.69/hr
   - **Budget Option**: RTX 4000 Ada (20GB VRAM) - $0.26/hr
   - **High-end**: RTX 6000 Ada (48GB VRAM) - $0.77/hr

   **Phase 2 - TFLite Conversion (15-30 minutes):**
   - **🎯 BEST VALUE**: H100 PCIe (80GB VRAM, 188GB RAM) - $2.39/hr
   - **PREMIUM**: H100 NVL (94GB VRAM) - $2.79/hr
   - **ALTERNATIVE**: H100 SXM (80GB VRAM, 125GB RAM) - $2.69/hr

   **💰 Cost Strategy**: Fine-tune on RTX 4090 (~$1-2), then switch to H100 for conversion (~$1-2)

### 1.2 Set Environment Variables
**CRITICAL**: You must set the HF_TOKEN environment variable for the notebooks to work.

1. In the pod configuration, find "Environment Variables" section
2. Add the following environment variable:
   - **Name**: `HF_TOKEN`
   - **Value**: Your Hugging Face token (starts with `hf_`)

### 1.3 Storage Configuration
- Set container disk to at least 50GB
- Set volume disk to at least 100GB (for model storage)

## 🔑 Step 2: Hugging Face Token Setup

### 2.1 Get Your Token
1. Go to [https://huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
2. Create a new token with "Read" permissions
3. Copy the token (starts with `hf_`)

### 2.2 Verify Token Access
Make sure your token has access to:
- `unsloth/gemma-3n-E4B-it`
- `tamazightdev/v5-gemma-3n-4b-tmz-ft-vllm-merged`
- `tamazightdev/v6-gemma-3n-2b-tmz-ft-vllm-merged`

## 💻 Step 3: Connect to Your Pod (EASY METHOD!)

### 3.1 🎯 Recommended: Jupyter Lab HTTP Connection (No SSH Required!)
**This is the EASIEST way to get started - no SSH setup needed!**

1. **Start your Runpod instance** and wait for it to show "Running" status
2. **Click "Connect"** on your pod
3. **Select "HTTP Services"** tab (not SSH!)
4. **Click "Jupyter Lab → 8888"** - this opens Jupyter Lab directly in your browser
5. **You're ready to go!** No passwords, no SSH keys, no terminal commands needed

### 3.2 ✅ Why Jupyter Lab HTTP is Better for Beginners:
- ✅ **No SSH setup required** - works immediately
- ✅ **No password prompts** - direct browser access
- ✅ **Perfect for notebooks** - designed for `.ipynb` files
- ✅ **Easy file upload** - drag and drop your files
- ✅ **Visual interface** - familiar file browser
- ✅ **No terminal knowledge needed** - point and click

### 3.3 Alternative: VSCode SSH Connection
If you specifically need VSCode, see `VSCODE_RUNPOD_BEGINNER_GUIDE.md` for SSH setup instructions.

## 📁 Step 4: Upload Your Notebook Files

### 4.1 Upload Your .ipynb Files (3 Easy Ways)

**Method 1: Upload Button (Easiest)**
1. In Jupyter Lab, click the **Upload button** (📁 icon) in the toolbar
2. Select your `.ipynb` file from your computer
3. File uploads to `/workspace` directory automatically
4. Double-click the file to open it

**Method 2: Drag and Drop**
1. Drag your `.ipynb` file from your computer
2. Drop it into the file browser panel (left side)
3. Double-click to open

**Method 3: Create New and Copy-Paste**
1. Click "Notebook" in the launcher (orange icon)
2. Copy content from your existing notebook and paste

### 4.2 Prepare Dataset Files (Optional)
If you have local dataset files:
- `107199_Tamazight_Dataset_train.jsonl`
- `10719_Tamazight_Dataset_eval.jsonl`

Upload them the same way as notebooks, or use the Hugging Face Hub versions (recommended).

## 🚀 Step 5: Running the Notebooks

### 5.1 Available Notebooks
1. **v6_Gemma_3N_(2B)_Conversational_072725_gguf_wkg.ipynb** - Fine-tuning 2B model (Recommended for beginners)
2. **v5_Gemma_3N_(4B)_Conversational_072725_gguf_wkg.ipynb** - Fine-tuning 4B model (Advanced users)
3. **v6-gemma-3n-2b-tflite-conversion-runpod-to-check.ipynb** - TFLite conversion for 2B model
4. **v5-gemma-3n-4b-tflite-conversion-runpod-to-check.ipynb** - TFLite conversion for 4B model

### 5.2 🎯 Recommended Workflow for Beginners
1. **Upload the 2B fine-tuning notebook** using Jupyter Lab upload
2. **Open the notebook** by double-clicking it
3. **Run cells one by one** - don't run all at once!
4. **Wait for fine-tuning to complete** (30-60 minutes)
5. **Upload the 2B TFLite conversion notebook**
6. **Run the conversion** to get mobile-ready files

### 5.3 Execution Order
1. **Start with fine-tuning**: Run the conversational notebook first
2. **Then convert to TFLite**: Run the appropriate TFLite conversion notebook
3. **Download your models**: Get the `.tflite` files for your mobile app

### 5.4 Key Features Updated for Runpod
✅ **Environment Variable Authentication**: Uses `HF_TOKEN` from pod settings
✅ **Runpod File Paths**: Updated from `/content/` to `./` or `/workspace/`
✅ **GPU Optimization**: Automatic GPU detection and memory reporting
✅ **Error Handling**: Better error messages for missing tokens/files
✅ **Flexible Dataset Loading**: Multiple options for dataset sources

## 🔍 Step 6: Verification

### 5.1 Check GPU Access
Run this in a notebook cell:
```python
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"GPU device: {torch.cuda.get_device_name(0)}")
print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
```

### 5.2 Check HF Authentication
Run this in a notebook cell:
```python
import os
from huggingface_hub import login

hf_token = os.getenv("HF_TOKEN")
if hf_token:
    login(token=hf_token)
    print("✅ Successfully authenticated with Hugging Face")
else:
    print("❌ HF_TOKEN not found")
```

### 5.3 Check Dataset Files
Run this in a notebook cell:
```python
import os
files = [f for f in os.listdir(".") if f.endswith(".jsonl")]
print("Dataset files found:", files)
```

## 🛠️ Troubleshooting

### Common Issues

**1. "HF_TOKEN not found"**
- Solution: Set the environment variable in pod settings and restart the pod

**2. "Dataset files not found"**
- Solution: Upload files to the correct directory or update file paths in notebook

**3. "CUDA out of memory"**
- Solution: Reduce batch size or use a larger GPU instance

**4. "Model not accessible"**
- Solution: Verify your HF token has access to the required models

### Getting Help
- Check the notebook output for detailed error messages
- Verify all setup steps were completed correctly
- Ensure your Runpod instance has sufficient GPU memory

## 📊 Expected Performance

### GPU Requirements
- **Minimum**: RTX 3090 (24GB VRAM)
- **Recommended**: RTX 4090 or A100 (40GB+ VRAM)
- **Training Time**: 30-60 minutes for fine-tuning
- **Conversion Time**: 5-15 minutes for TFLite conversion

### Output Files
After successful execution, you'll have:
- Fine-tuned model files (`.safetensors`, `.json`)
- TFLite model files (`.tflite`)
- Tokenizer assets for mobile deployment

## 🎯 Competition Notes

- **Deadline**: August 06, 2025
- **Focus**: Tamazight language processing
- **Output**: Mobile-ready TFLite models
- **Platform**: Optimized for Runpod GPU instances

Good luck with the competition! 🏆
