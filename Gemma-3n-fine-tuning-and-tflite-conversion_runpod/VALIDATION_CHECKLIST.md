# ✅ Runpod Notebook Validation Checklist

**Date**: July 31, 2025  
**Competition**: <PERSON><PERSON> Google Deepmind Competition Hackathon  
**Deadline**: August 06, 2025

## 📋 Notebooks Updated for Runpod

### 1. TFLite Conversion Notebooks

#### ✅ v5-gemma-3n-4b-tflite-conversion-runpod-to-check.ipynb
- **Status**: ✅ UPDATED FOR RUNPOD
- **Changes Made**:
  - Added GPU detection and memory reporting
  - Enhanced HF_TOKEN authentication with detailed instructions
  - Added troubleshooting guidance
  - Included `huggingface_hub` in pip install
  - Added fallback authentication options

#### ✅ v6-gemma-3n-2b-tflite-conversion-runpod-to-check.ipynb  
- **Status**: ✅ UPDATED FOR RUNPOD
- **Changes Made**:
  - Same improvements as v5 notebook
  - Fixed output filename (2B instead of 4B)
  - Proper model ID for 2B version

### 2. Fine-tuning Notebook

#### ✅ v5_Gemma_3N_(4B)_Conversational_072725_gguf_wkg.ipynb
- **Status**: ✅ MAJOR UPDATES FOR RUNPOD
- **Changes Made**:
  - **Header**: Updated to indicate Runpod optimization
  - **Installation**: Removed Colab detection, direct Unsloth install
  - **Authentication**: Added HF_TOKEN environment variable setup
  - **Dataset Loading**: Updated paths from `/content/` to `./` with multiple options
  - **Token Management**: Replaced hardcoded tokens with environment variables
  - **Error Handling**: Enhanced error messages and troubleshooting

## 🔧 Key Runpod Optimizations

### Authentication System
```python
# Environment variable based authentication
hf_token = os.getenv("HF_TOKEN")
if hf_token:
    login(token=hf_token)
    print("✅ Successfully authenticated with Hugging Face")
```

### Dataset Loading Options
```python
# Option 1: Local files in Runpod workspace
train_file = "./107199_Tamazight_Dataset_train.jsonl"
test_file = "./10719_Tamazight_Dataset_eval.jsonl"

# Option 2: Hugging Face Hub
# dataset = load_dataset("tamazightdev/tamazight-dataset")

# Option 3: Alternative workspace paths
# train_file = "/workspace/107199_Tamazight_Dataset_train.jsonl"
```

### GPU Verification
```python
# Automatic GPU detection and reporting
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"GPU device: {torch.cuda.get_device_name(0)}")
print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
```

## 🚨 Critical Setup Requirements

### 1. Environment Variables (MANDATORY)
- **HF_TOKEN**: Must be set in Runpod pod settings
- **Value**: Your Hugging Face token from https://huggingface.co/settings/tokens
- **Restart**: Pod must be restarted after adding environment variables

### 2. Dataset Files
- Upload to Runpod workspace root directory
- Required files:
  - `107199_Tamazight_Dataset_train.jsonl`
  - `10719_Tamazight_Dataset_eval.jsonl`

### 3. GPU Requirements
- **Minimum**: RTX 3090 (24GB VRAM)
- **Recommended**: RTX 4090 or A100 (40GB+ VRAM)

## 🔍 Validation Tests

### Test 1: Environment Setup
```python
import os
import torch
from huggingface_hub import login

# Check GPU
assert torch.cuda.is_available(), "GPU not available"
print(f"✅ GPU: {torch.cuda.get_device_name(0)}")

# Check HF Token
hf_token = os.getenv("HF_TOKEN")
assert hf_token is not None, "HF_TOKEN not set"
login(token=hf_token)
print("✅ HF Authentication successful")
```

### Test 2: Dataset Access
```python
import os
from datasets import load_dataset

# Check local files
train_file = "./107199_Tamazight_Dataset_train.jsonl"
test_file = "./10719_Tamazight_Dataset_eval.jsonl"

assert os.path.exists(train_file), f"Train file not found: {train_file}"
assert os.path.exists(test_file), f"Test file not found: {test_file}"

# Load dataset
dataset = load_dataset("json", data_files={"train": train_file, "test": test_file})
print(f"✅ Dataset loaded: {len(dataset['train'])} train, {len(dataset['test'])} test")
```

### Test 3: Model Loading
```python
from unsloth import FastModel

# Load model with token
model, tokenizer = FastModel.from_pretrained(
    model_name="unsloth/gemma-3n-E4B-it",
    max_seq_length=1024,
    load_in_4bit=True,
    token=hf_token
)
print("✅ Model loaded successfully")
```

## 📊 Expected Execution Times

### Fine-tuning (v5 notebook)
- **Setup**: 5-10 minutes
- **Training**: 30-60 minutes (depending on GPU)
- **Saving**: 5-10 minutes
- **Total**: ~1-1.5 hours

### TFLite Conversion (v5/v6 notebooks)
- **Setup**: 2-5 minutes
- **Model Loading**: 5-10 minutes
- **Conversion**: 5-15 minutes
- **Total**: ~15-30 minutes

## 🛠️ Common Issues & Solutions

### Issue 1: "HF_TOKEN not found"
**Solution**: 
1. Set environment variable in Runpod pod settings
2. Restart the pod
3. Verify with `os.getenv("HF_TOKEN")`

### Issue 2: "Dataset files not found"
**Solution**:
1. Upload files to workspace root directory
2. Check file names match exactly
3. Use `os.listdir(".")` to verify

### Issue 3: "CUDA out of memory"
**Solution**:
1. Reduce batch size in training config
2. Use smaller model (2B instead of 4B)
3. Upgrade to larger GPU instance

### Issue 4: "Model not accessible"
**Solution**:
1. Verify HF token permissions
2. Check model repository access
3. Ensure token is not expired

## 🎯 Competition Readiness

### ✅ All notebooks are now optimized for:
- Runpod GPU environment
- Environment variable authentication
- Flexible dataset loading
- Enhanced error handling
- GPU memory optimization

### 📁 Deliverables Ready:
- Fine-tuned Gemma 3N models for Tamazight
- TFLite models for mobile deployment
- Tokenizer assets for app integration

### 🏆 Competition Timeline:
- **Today (July 31, 2025)**: Setup and initial runs
- **August 1-5, 2025**: Fine-tuning and optimization
- **August 6, 2025**: Final submission deadline

**Status**: ✅ READY FOR COMPETITION
