To enable drag-and-drop upload of your `.ipynb` file using VS Code on a RunPod pod, you need to:

1. **Deploy a Pod with VS Code Enabled**:  
   When creating your pod, select a template that supports VS Code (for example, the VS Code Server template or any template with SSH Terminal Access enabled). Make sure the "SSH Terminal Access" option is checked during deployment. This ensures you can connect via VS Code’s Remote-SSH extension and access the pod’s file system directly [Connect to a Pod with VSCode or Cursor](https://docs.runpod.io/pods/configuration/connect-to-ide#page-title).

2. **Connect to the Pod via VS Code**:  
   - Install the Remote-SSH extension in your local VS Code.
   - Add your SSH key to your RunPod account before starting the pod.
   - Use the SSH command provided in your pod’s "Connect" tab (under "SSH over exposed TCP") to add the pod as a host in VS Code.
   - Use "Remote-SSH: Connect to Host..." from the Command Palette to connect to your pod [Step 4: Configure SSH for your IDE](https://docs.runpod.io/pods/configuration/connect-to-ide#step-4%3A-configure-ssh-for-your-ide).

3. **Drag and Drop Your File**:  
   Once connected, you can open the `/workspace` directory (the default persistent storage). In the VS Code file explorer, simply drag and drop your `.ipynb` file from your local machine into the desired folder in the pod. The upload speed will depend on your local internet connection [Upload Models through VSCode](https://www.runpod.io/blog/better-forge-stable-diffusion).

**Note:** If you do not see the option to connect via SSH over exposed TCP, your pod template may not support direct VS Code connections. In that case, redeploy with a compatible template.

This setup allows you to easily upload, edit, and manage your `.ipynb` notebooks directly from your local VS Code environment on your RunPod pod.