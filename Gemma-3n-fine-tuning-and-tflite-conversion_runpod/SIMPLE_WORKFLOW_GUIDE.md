# 🎯 Simple 3-Step Workflow: Fine-tune → Upload → Convert

**For Complete Beginners | Kaggle Competition Ready**

## 📋 Quick Overview

This is the **simplest possible workflow** to fine-tune Tamazight models and get TFLite files for mobile apps.

### What You'll Get:
- ✅ Fine-tuned Gemma models for Tamazight
- ✅ Models automatically uploaded to Hugging Face
- ✅ TFLite files ready for mobile apps
- ✅ Complete in 2-3 hours total

## 🚀 STEP 1: Choose Your Model Size

### For Beginners (Recommended):
**Use 2B Model** - Faster, cheaper, easier to learn
- Notebook: `v6_Gemma_3N_(2B)_Conversational_072725_gguf_wkg.ipynb`
- Time: ~45 minutes
- Cost: ~$3-5
- Memory: Works on most GPUs

### For Advanced Users:
**Use 4B Model** - Better quality, more memory needed
- Notebook: `v5_Gemma_3N_(4B)_Conversational_072725_gguf_wkg.ipynb`
- Time: ~90 minutes
- Cost: ~$8-12
- Memory: Needs RTX 4090 or A100

## 🔧 STEP 2: Set Up Runpod (One Time Only)

### 2.1 Environment Variable Setup
**CRITICAL**: Set this in your Runpod pod settings:
```
Name: HF_TOKEN
Value: hf_your_token_here
```

### 2.2 Pod Deployment Steps
1. Click "**Pods**" in the left sidebar
2. Click "**Deploy a Pod**"
3. Choose "**GPU**" option

### 2.3 Template Selection (IMPORTANT)
**🎯 RECOMMENDED**: "**Runpod VS Code Server**" (Perfect for VSCode workflow)
**Alternative**: "**Runpod PyTorch 2.8.0**" (Latest PyTorch version)
**❌ Avoid**: Older PyTorch versions (2.1, 2.2.0, 2.4.0) - may cause issues

### 2.4 GPU Selection (Two-Phase Strategy)

**🔥 Fine-tuning Phase:**
- **🎯 PERFECT**: RTX 4090 (24GB) - $0.69/hr
- **Budget**: RTX 4000 Ada (20GB) - $0.26/hr

**🚀 TFLite Conversion Phase:**
- **🎯 BEST**: H100 PCIe (80GB, 188GB RAM) - $2.39/hr
- **Premium**: H100 NVL (94GB) - $2.79/hr

**💡 Strategy**: Start with RTX 4090, switch to H100 for conversion!

### 2.4 Storage Settings
- Container Disk: 50GB
- Volume Disk: 100GB

## 📊 STEP 3: Execute the Complete Pipeline

### Phase 1: Fine-tuning (Choose ONE)

#### Option A: 2B Model (Beginner Friendly)
```bash
# Open this notebook in VSCode:
v6_Gemma_3N_(2B)_Conversational_072725_gguf_wkg.ipynb

# Run all cells in order - the notebook will:
# ✅ Install dependencies
# ✅ Load datasets from HF Hub automatically
# ✅ Fine-tune the 2B model
# ✅ Upload to these repositories:
#    - tamazightdev/v6-gemma-3n-2b-tmz-ft-pt-lora
#    - tamazightdev/v6-gemma-3n-2b-tmz-ft-vllm-merged
#    - tamazightdev/v6-gemma-3n-2b-tmz-ft-gguf
```

#### Option B: 4B Model (Advanced)
```bash
# Open this notebook in VSCode:
v5_Gemma_3N_(4B)_Conversational_072725_gguf_wkg.ipynb

# Run all cells in order - the notebook will:
# ✅ Install dependencies
# ✅ Load datasets from HF Hub automatically
# ✅ Fine-tune the 4B model
# ✅ Upload to these repositories:
#    - tamazightdev/v5-gemma-3n-4b-tmz-ft-pt-lora
#    - tamazightdev/v5-gemma-3n-4b-tmz-ft-vllm-merged
#    - tamazightdev/v5-gemma-3n-E4b-tmz-ft-gguf
```

### Phase 2: TFLite Conversion

#### For 2B Model:
```bash
# Open this notebook in VSCode:
v6-gemma-3n-2b-tflite-conversion-runpod-to-check.ipynb

# Run all cells - the notebook will:
# ✅ Load your fine-tuned model from HF Hub
# ✅ Convert to TFLite format
# ✅ Generate mobile-ready files:
#    - gemma-3n-2b-tamazight-ft.tflite
#    - tokenizer_assets/ folder
```

#### For 4B Model:
```bash
# Open this notebook in VSCode:
v5-gemma-3n-4b-tflite-conversion-runpod-to-check.ipynb

# Run all cells - the notebook will:
# ✅ Load your fine-tuned model from HF Hub
# ✅ Convert to TFLite format
# ✅ Generate mobile-ready files:
#    - gemma-3n-4b-tamazight-ft.tflite
#    - tokenizer_assets/ folder
```

## ⏱️ Timeline & Expectations

### 2B Model Workflow:
1. **Setup**: 5 minutes
2. **Fine-tuning**: 45 minutes
3. **TFLite Conversion**: 10 minutes
4. **Total**: ~1 hour

### 4B Model Workflow:
1. **Setup**: 5 minutes
2. **Fine-tuning**: 90 minutes
3. **TFLite Conversion**: 15 minutes
4. **Total**: ~2 hours

## 📁 What You'll Get

### After Fine-tuning:
Your Hugging Face account will have these new repositories:
- **LoRA Model**: For further fine-tuning
- **Merged Model**: For inference and TFLite conversion
- **GGUF Model**: For local inference with llama.cpp/Ollama

### After TFLite Conversion:
You'll download these files:
- **`.tflite` file**: The mobile model
- **`tokenizer_assets/` folder**: Text processing files

## 🔍 Verification Checklist

### ✅ After Fine-tuning:
- [ ] Check your HF profile - new repositories should appear
- [ ] Models should have recent "Updated X hours ago" timestamps
- [ ] No error messages in notebook output

### ✅ After TFLite Conversion:
- [ ] `.tflite` file downloaded (should be 2-8GB)
- [ ] `tokenizer_assets` folder downloaded
- [ ] Files can be opened/inspected

## 🚨 Common Beginner Mistakes

### ❌ Don't Do This:
1. **Skip environment variable setup** - Models won't upload
2. **Run all cells at once** - May cause memory issues
3. **Use insufficient GPU** - Will run out of memory
4. **Forget to download TFLite files** - You'll lose your work

### ✅ Do This Instead:
1. **Set HF_TOKEN first** - Before starting any notebook
2. **Run cells one by one** - Read output before proceeding
3. **Use recommended GPU sizes** - RTX 4090 or A100
4. **Download files immediately** - After TFLite conversion

## 🛠️ Quick Troubleshooting

### Problem: "HF_TOKEN not found"
**Solution**: Go to Runpod pod settings → Environment Variables → Add HF_TOKEN → Restart pod

### Problem: "CUDA out of memory"
**Solution**: Use 2B model instead of 4B, or upgrade to larger GPU

### Problem: "Dataset loading failed"
**Solution**: Check HF_TOKEN is correct, verify internet connection

### Problem: "Model upload failed"
**Solution**: Verify HF_TOKEN has write permissions, check repository exists

## 🎯 Success Indicators

### You're Successful When:
1. **Fine-tuning notebook completes** without errors
2. **New repositories appear** in your HF profile
3. **TFLite conversion completes** without errors
4. **Files download successfully** to your local machine

### Ready for Competition When:
- [ ] You have working TFLite models
- [ ] Models are uploaded to Hugging Face
- [ ] You understand the complete workflow
- [ ] You can reproduce the process

## 📞 Getting Help

### If You Get Stuck:
1. **Read error messages carefully** - They usually tell you what's wrong
2. **Check the troubleshooting section** above
3. **Verify your setup** - HF_TOKEN, GPU, storage
4. **Ask on Discord** - Competition community is helpful

### Before Asking for Help:
- [ ] Screenshot of the error
- [ ] Which notebook you're using
- [ ] Which cell failed
- [ ] Your GPU type and memory

## 🏆 Competition Tips

1. **Start with 2B model** to learn the process
2. **Test the complete workflow** before the deadline
3. **Keep backups** of successful models
4. **Document what works** for future reference

**You've got this!** 🚀

---

*This workflow has been tested and optimized for beginners. Follow it step-by-step and you'll have working Tamazight models for the competition.*
