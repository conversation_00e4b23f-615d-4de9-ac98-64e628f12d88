# TFLite Integration Guide - Gemma-3n 4b Implementation

## 📋 Overview

This guide provides step-by-step instructions for integrating the fine-tuned Gemma-3n 4b TensorFlow Lite model into the Tamazight MultiLingo app after the model conversion is completed.

## 🗂️ Native Code Files Created

### Android Native Module Files
- `android/TfliteModule.java` - Main inference module
- `android/TflitePackage.java` - React Native package registration
- `android/MainApplication.java` - Updated with TflitePackage
- `android/build.gradle` - Updated with TensorFlow Lite dependencies

### TypeScript Integration
- `services/nativeTfliteService.ts` - TypeScript wrapper for native module

## 🚀 Integration Steps

### Step 1: Place Model Files
```bash
# After TFLite conversion is complete, place these files:
assets/ml/gemma-3n-4b-tamazight-ft.tflite    # Main model file (~2-4GB)
assets/ml/tokenizer.json                      # Tokenizer configuration
assets/ml/vocab.txt                          # Vocabulary (if separate)
```

### Step 2: Copy Native Android Files
```bash
# Create Android project structure if it doesn't exist
npx expo run:android --no-build-cache

# Copy the native files to correct locations:
# android/TfliteModule.java → android/app/src/main/java/com/tamazighttranslate/
# android/TflitePackage.java → android/app/src/main/java/com/tamazighttranslate/
# android/MainApplication.java → android/app/src/main/java/com/tamazighttranslate/
# android/build.gradle → android/app/build.gradle
```

### Step 3: Update Package Name
Replace `com.tamazighttranslate` in all Java files with your actual package name:
```java
// Find your package name in android/app/src/main/AndroidManifest.xml
// Update all Java files accordingly
```

### Step 4: Update Offline AI Service
```typescript
// In services/offlineAIService.ts, replace mock translations with:
import { nativeTfliteService } from './nativeTfliteService';

async translateText(text, fromLang, toLang, context) {
  if (nativeTfliteService.isAvailable()) {
    return await nativeTfliteService.translateText(text, fromLang, toLang, context);
  } else {
    // Fallback to mock translations
    return this.mockTranslation(text, fromLang, toLang);
  }
}
```

### Step 5: Initialize Native Service
```typescript
// In app/_layout.tsx, add native service initialization:
import { nativeTfliteService } from '@/services/nativeTfliteService';

useEffect(() => {
  const initializeApp = async () => {
    try {
      // Initialize database
      await databaseService.initialize();
      
      // Initialize offline AI service (mock)
      await offlineAIService.initialize();
      
      // Initialize native TFLite service (real model)
      if (nativeTfliteService.isAvailable()) {
        await nativeTfliteService.initialize();
        console.log('Native TFLite service ready');
      }
      
      // Preload emergency phrases
      await offlineAIService.preloadEmergencyPhrases();
    } catch (error) {
      console.error('Failed to initialize app services:', error);
    }
  };
  
  initializeApp();
}, []);
```

## 🔧 Build Configuration

### Android Dependencies Added
```gradle
// In android/app/build.gradle
dependencies {
    // TensorFlow Lite core
    implementation 'org.tensorflow:tensorflow-lite:2.16.1'
    implementation 'org.tensorflow:tensorflow-lite-gpu:2.16.1'
    implementation 'org.tensorflow:tensorflow-lite-support:0.4.4'
    
    // Google AI Edge SDK
    implementation 'com.google.android.gms:play-services-tflite-java:16.3.0'
    implementation 'com.google.android.gms:play-services-tflite-gpu:16.3.0'
    
    // JSON parsing for tokenizer
    implementation 'com.google.code.gson:gson:2.10.1'
}
```

### Metro Configuration
```javascript
// Already configured in metro.config.js
config.resolver.assetExts.push('tflite', 'task');
```

## 🧪 Testing Steps

### 1. Build and Test
```bash
# Clean build
npx expo run:android --clear

# Test model loading
# Check logs for "Native TFLite model initialized"
```

### 2. Test Translation
```typescript
// Test in app
const result = await nativeTfliteService.translateText(
  "I need help",
  "english", 
  "tamazight",
  "emergency"
);
console.log(result); // Should show actual translation
```

### 3. Performance Monitoring
```typescript
// Check performance metrics
const info = await nativeTfliteService.getModelInfo();
console.log('Model info:', info);

// Monitor translation times (target: <2 seconds)
// Monitor memory usage (target: <4GB)
```

## 🚨 Emergency Phrase Testing

### High Priority Test Cases
```typescript
const emergencyPhrases = [
  "I need help",
  "Where is the hospital?",
  "Call the police", 
  "I am lost",
  "Water",
  "Food"
];

for (const phrase of emergencyPhrases) {
  const result = await nativeTfliteService.translateText(
    phrase, 
    "english", 
    "tamazight", 
    "emergency"
  );
  console.log(`${phrase} → ${result.translatedText}`);
  // Verify accuracy > 95% for emergency phrases
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Model Not Loading**
   ```bash
   # Check file placement
   ls -la assets/ml/
   
   # Check Android logs
   npx react-native log-android
   ```

2. **Native Module Not Found**
   ```bash
   # Rebuild with clean cache
   npx expo run:android --clear
   
   # Check package registration in MainApplication.java
   ```

3. **Out of Memory**
   ```java
   // In android/app/src/main/AndroidManifest.xml
   <application android:largeHeap="true">
   ```

4. **GPU Acceleration Issues**
   ```java
   // Fallback to CPU in TfliteModule.java
   options.setNumThreads(4); // Instead of GPU delegate
   ```

## 📊 Performance Targets

### Inference Metrics
- **Translation Time**: < 2 seconds on modern Android devices
- **Memory Usage**: < 4GB RAM total
- **Emergency Accuracy**: > 95% for critical phrases
- **General Accuracy**: > 85% for common phrases

### Monitoring Code
```typescript
// Add to translation calls
const startTime = Date.now();
const result = await nativeTfliteService.translateText(...);
const endTime = Date.now();

console.log(`Translation time: ${endTime - startTime}ms`);
console.log(`Confidence: ${result.confidence}`);
```

## ✅ Integration Checklist

- [ ] Model files placed in `assets/ml/`
- [ ] Native Android files copied to correct locations
- [ ] Package names updated in Java files
- [ ] Build.gradle dependencies added
- [ ] Native service integrated with offline AI service
- [ ] App initialization updated
- [ ] Emergency phrases tested
- [ ] Performance metrics verified
- [ ] Memory usage optimized
- [ ] Error handling implemented

## 🎯 Final Validation

### Before Hackathon Submission
1. Test all language pairs (Tamazight ↔ Arabic ↔ French ↔ English)
2. Verify emergency context prioritization
3. Test offline functionality (airplane mode)
4. Validate cultural accuracy with native speakers
5. Performance test on various Android devices
6. Memory leak testing for extended usage

---

**Ready for Integration**: All native code is prepared and waiting for the TFLite model files. The integration should take approximately 2-3 hours once the model conversion is complete.
