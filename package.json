{"name": "tamazight-multi-lingo-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@google/generative-ai": "^0.24.1", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "convex": "^1.25.4", "expo": "^53.0.20", "expo-av": "~15.1.7", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.525.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.28.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}